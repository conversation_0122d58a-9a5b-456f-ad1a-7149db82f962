import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd


data=pd.read_csv(r'C:\50-ML-Challenge-\dataset\Social_Network_Ads (1).csv')
data.head()

data=data.drop(columns=["User ID","Gender"],axis=1)

x=data.drop(columns="Purchased",axis=1)
y=data["Purchased"]



from sklearn.model_selection import train_test_split
xtrain,xtest,ytrain,ytest=train_test_split(x,y,test_size=0.2,random_state=42)

from sklearn.preprocessing import StandardScaler
sc=StandardScaler()
xtrain = sc.fit_transform(xtrain)   # fit + transform training data
xtest = sc.transform(xtest)         # only transform test data


from sklearn.linear_model import LogisticRegression
model=LogisticRegression()
model=model.fit(xtrain,ytrain)


ypred=model.predict(xtest)


from sklearn.metrics import confusion_matrix
cm=confusion_matrix(ytest,ypred)

sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
plt.title("Confusion Matrix - Test Data")
plt.xlabel("Predicted")
plt.ylabel("Actual")
plt.show()

