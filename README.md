
# 🧠 50 Days of Machine Learning Challenge  

Welcome to my **50 Days of Machine Learning Challenge**! 🚀  
This repository documents my daily progress as I learn, practice, and apply key concepts in **Machine Learning**.  

I’m following the [100 Days of ML Code by <PERSON><PERSON>](https://github.com/<PERSON><PERSON>-<PERSON>/100-Days-Of-ML-Code/tree/master) and adapting it into **50 focused days**.  
Each day, I’ll share notes, code, and small projects to track my journey and help others who want to learn alongside me.  

---

## 📌 Why this challenge?  
- To build a **solid foundation** in ML.  
- To practice **coding ML algorithms** and understanding them step by step.  
- To create a **public learning log** that can help others.  
- To prepare for **future ML & Data Science projects**.  

---

## 📂 Repository Structure  
- **Day X/** → Contains code, notes, and explanations for the day’s topic.  
- **Resources/** → Helpful links, tutorials, and references.  
- **Mini Projects/** → Small ML implementations and case studies.  

---

## ✅ Topics Covered  
Over the 50 days, I’ll be covering:  
- Machine Learning Basics  
- Data Preprocessing & Visualization  
- Supervised Learning (Regression, Classification)  
- Unsupervised Learning (Clustering, Dimensionality Reduction)  
- Model Evaluation & Tuning  
- Feature Engineering  
- Introduction to Deep Learning  
- Real-world Mini Projects  

---

## 🙌 Who is this for?  
- Beginners starting their ML journey.  
- Students who want **structured daily practice**.  
- Anyone curious about applying ML step by step.  

---

## 🌟 How this can help you  
- Learn ML concepts in **bite-sized daily steps**.  
- See **practical code examples** for each concept.  
- Get a **roadmap for your own ML journey**.  
- Explore curated resources and projects.  

---

## 📅 Progress  
I’ll update this repo daily with my learnings.  
- [Done] Day 1 →you will learn SimpleImputer for missing values,LabelEncoder,StandardScaler  
- [ ] Day 2 → Linear Regression Model 
- [ ] Day 3 → …  
- …  
- [ ] Day 50 → 🎉  

---

## 🔗 Connect with Me  
If you’re also doing the challenge or just curious, feel free to connect!  

- 📧 Email: <EMAIL>  
- 💼 LinkedIn: https://www.linkedin.com/in/sama-hassans/  
- 🐙 GitHub: https://github.com/SamaHassa 
  

