import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score,f1_score,confusion_matrix

data=pd.read_csv(r"C:\50-ML-Challenge-\dataset\KNN_Dataset.csv")
data

data.isnull().sum()

zero_not_accepted=["Glucose","BloodPressure","Insulin","BMI","SkinThickness"]

for column in zero_not_accepted:
  data[column]=data[column].replace(0,np.nan)
  mean=int(data[column].mean(skipna=True))
  data[column]=data[column].replace(np.nan,mean)

#split data
x=data.iloc[:,:8]
y=data.iloc[:,9]


y

