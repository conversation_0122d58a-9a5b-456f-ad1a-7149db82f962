{"cells": [{"cell_type": "markdown", "id": "ce3321d5", "metadata": {}, "source": ["![Data Preprocessing Day 2](https://github.com/<PERSON><PERSON>-<PERSON>/100-Days-Of-ML-Code/raw/master/Info-graphs/Day%202.jpg)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "1497e3d0", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n"]}, {"cell_type": "code", "execution_count": 3, "id": "428db9c6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Hours</th>\n", "      <th>Scores</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.5</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.1</td>\n", "      <td>47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.2</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8.5</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3.5</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1.5</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>9.2</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>5.5</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>8.3</td>\n", "      <td>81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2.7</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>7.7</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>5.9</td>\n", "      <td>62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>4.5</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>3.3</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>1.1</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>8.9</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2.5</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>1.9</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>6.1</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>7.4</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2.7</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>4.8</td>\n", "      <td>54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>3.8</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>6.9</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>7.8</td>\n", "      <td>86</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Hours  Scores\n", "0     2.5      21\n", "1     5.1      47\n", "2     3.2      27\n", "3     8.5      75\n", "4     3.5      30\n", "5     1.5      20\n", "6     9.2      88\n", "7     5.5      60\n", "8     8.3      81\n", "9     2.7      25\n", "10    7.7      85\n", "11    5.9      62\n", "12    4.5      41\n", "13    3.3      42\n", "14    1.1      17\n", "15    8.9      95\n", "16    2.5      30\n", "17    1.9      24\n", "18    6.1      67\n", "19    7.4      69\n", "20    2.7      30\n", "21    4.8      54\n", "22    3.8      35\n", "23    6.9      76\n", "24    7.8      86"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv(r\"C:\\50-ML-Challenge-\\dataset\\studentscores.csv\",header=0, sep=\",\")\n", "data"]}, {"cell_type": "code", "execution_count": 36, "id": "57c1fe6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 25 entries, 0 to 24\n", "Data columns (total 2 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   Hours   25 non-null     float64\n", " 1   Scores  25 non-null     int64  \n", "dtypes: float64(1), int64(1)\n", "memory usage: 528.0 bytes\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 37, "id": "df343a07", "metadata": {}, "outputs": [{"data": {"text/plain": ["Hours     0\n", "Scores    0\n", "dtype: int64"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["data.isnull().sum()"]}, {"cell_type": "code", "execution_count": 48, "id": "f44c08ca", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "x=data.drop(columns=\"Scores\",axis=1)\n", "y=data[\"Scores\"]\n", "x_train,x_test,y_train,y_test=train_test_split(x,y,test_size=0.3 ,random_state=42)"]}, {"cell_type": "code", "execution_count": 49, "id": "8d88102f", "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LinearRegression\n", "model=LinearRegression()\n", "model=model.fit(x_train,y_train)\n"]}, {"cell_type": "code", "execution_count": 50, "id": "7d2931b1", "metadata": {}, "outputs": [], "source": ["y_pred=model.predict(x_test)"]}, {"cell_type": "code", "execution_count": 51, "id": "65501056", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Training Results\n", "plt.scatter(x_train, y_train, color='r', marker=\"o\")  # training data\n", "plt.plot(x_train, model.predict(x_train), color=\"b\")  # regression line\n", "plt.xlabel(\"X\")\n", "plt.ylabel(\"Y\")\n", "plt.title(\"Train Results\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 52, "id": "8e6e42a8", "metadata": {}, "outputs": [{"data": {"image/png": "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***************************************/i0FKQcrvdeuutt3TixAklJydbPU7AS0tLU/fu3dWlSxerR7GNXbt2qWHDhrr00kvVr18/7d+/3+qRAt7777+vtm3bqk+fPmrQoIGuuuoqzZ492+qxbOX06dN6/fXXNXjw4CrzIODyuvbaa7Vs2TJ9//33kqQtW7Zo9erVuvnmmy2ezLdoZILMtm3blJycrFOnTql27dpatGiRWrRoYfVYAe2tt97S5s2blZGRYfUotnHNNddo7ty5atasmbKysjRx4kSlpKRo+/btCg8Pt3q8gPXjjz9q5syZGj16tP72t78pIyNDw4cPV0hIiAYMGGD1eLbw7rvvKjc3VwMHDrR6lIA3ZswYuVwuNW/eXE6nU263W08++aT69etn9Wi+ZSCo5OfnG7t27TI2btxojBkzxrj44ouNb775xuqxAtb+/fuNBg0aGFu2bClau/76640RI0ZYN5QNHT161IiIiDD++9//Wj1KQKtRo4aRnJzssTZs2DCjffv2Fk1kP127djVuueUWq8ewhTfffNOIi4sz3nzzTWPr1q3Gq6++atSrV8+YO3eu1aP5FI1MkAkJCdFll10mSWrTpo0yMjI0bdo0vfjiixZPFpg2bdqkw4cP6+qrry5ac7vd+uKLLzR9+nTl5+fL6XRaOKE91KlTR1dccYV2795t9SgBLTY21qshTUxM1MKFCy2ayF727dunpUuXKj093epRbOHhhx/WmDFjdMcdd0iSWrVqpX379mnSpElB1QASZIJcYWGh8vPzrR4jYN14443atm2bx9qgQYPUvHlzPfroo4SYUjp+/Lh++OEH3X333VaPEtA6dOignTt3eqx9//33uuSSSyyayF7mzJmjBg0aFJ28it938uRJVavmeSqs0+lUYWGhRRNVDoJMEBk7dqxuvvlmNW7cWMeOHdO8efO0YsUKffrpp1aPFrDCw8PVsmVLj7VatWqpfv36Xuv4zUMPPaQePXrokksu0aFDh/TYY4/J6XSqb9++Vo8W0EaNGqVrr71WTz31lG6//XZt2LBBs2bN0qxZs6weLeAVFhZqzpw5GjBgAJf6l1KPHj305JNPqnHjxkpKStJXX32lZ555RoMHD7Z6NJ/i/4YgcvjwYfXv319ZWVmKjIxU69at9emnn+qmm26yejQEmQMHDqhv3776+eefFRUVpY4dO2rdunVel8nCU7t27bRo0SKNHTtW//znP5WQkKCpU6cG38mXlWDp0qXav39/0P0QrkzPP/+8xo0bp/vvv1+HDx9Ww4YNNXToUI0fP97q0XzKYRhBdos/AABQZXAfGQAAYFsEGQAAYFsEGQAAYFsEGQAAYFsEGQAAYFsEGQAAYFsEGQAAYFsEGQAAYFsEGQC24na7de211yo1NdVjPS8vT/Hx8fr73/9u0WQArMCdfQHYzvfff68rr7xSs2fPLrq9f//+/bVlyxZlZGQoJCTE4gkB+AtBBoAtPffcc5owYYK++eYbbdiwQX369FFGRob+8Ic/WD0aAD8iyACwJcMwdMMNN8jpdGrbtm0aNmyY/vGPf1g9FgA/I8gAsK0dO3YoMTFRrVq10ubNm1W9enWrRwLgZ5zsC8C2Xn75ZV100UXas2ePDhw4YPU4ACxAIwPAltasWaPrr79en332mZ544glJ0tKlS+VwOCyeDIA/0cgAsJ2TJ09q4MCBuu+++9S5c2e99NJL2rBhg1544QWrRwPgZzQyAGxnxIgR+vjjj7VlyxZddNFFkqQXX3xRDz30kLZt26YmTZpYOyAAvyHIALCVlStX6sYbb9SKFSvUsWNHj9e6deumM2fOcIgJqEIIMgAAwLY4RwYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANgWQQYAANjW/wdY/MuPYce98gAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Test Results\n", "plt.scatter(x_test, y_test, color='r', marker=\"o\")  # training data\n", "plt.plot(x_test, y_pred, color=\"b\")  # regression line\n", "plt.xlabel(\"X\")\n", "plt.ylabel(\"Y\")\n", "plt.title(\"Test Results\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e9b6c106", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3eb765e4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (my_env)", "language": "python", "name": "my_env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}