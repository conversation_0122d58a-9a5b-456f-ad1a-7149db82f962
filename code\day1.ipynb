{"cells": [{"cell_type": "markdown", "id": "9bae4bf0", "metadata": {}, "source": ["![Data Preprocessing Day 1](https://github.com/<PERSON><PERSON>-<PERSON>/100-Days-Of-ML-Code/blob/master/Info-graphs/Day%201.jpg?raw=true)\n"]}, {"cell_type": "code", "execution_count": 64, "id": "f56cef76", "metadata": {}, "outputs": [], "source": ["#Step 1\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 65, "id": "d6865cb3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>Age</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Purchased</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>France</td>\n", "      <td>44.0</td>\n", "      <td>72000.0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Spain</td>\n", "      <td>27.0</td>\n", "      <td>48000.0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Germany</td>\n", "      <td>30.0</td>\n", "      <td>54000.0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Spain</td>\n", "      <td>38.0</td>\n", "      <td>61000.0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Germany</td>\n", "      <td>40.0</td>\n", "      <td>NaN</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>France</td>\n", "      <td>35.0</td>\n", "      <td>58000.0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Spain</td>\n", "      <td>NaN</td>\n", "      <td>52000.0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>France</td>\n", "      <td>48.0</td>\n", "      <td>79000.0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Germany</td>\n", "      <td>50.0</td>\n", "      <td>83000.0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>France</td>\n", "      <td>37.0</td>\n", "      <td>67000.0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Country   Age   Salary Purchased\n", "0   France  44.0  72000.0        No\n", "1    Spain  27.0  48000.0       Yes\n", "2  Germany  30.0  54000.0        No\n", "3    Spain  38.0  61000.0        No\n", "4  Germany  40.0      NaN       Yes\n", "5   France  35.0  58000.0       Yes\n", "6    Spain   NaN  52000.0        No\n", "7   France  48.0  79000.0       Yes\n", "8  Germany  50.0  83000.0        No\n", "9   France  37.0  67000.0       Yes"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["#Step 2\n", "\n", "data = pd.read_csv(r\"C:\\50-ML-Challenge-\\dataset\\Data.csv\")\n", "data"]}, {"cell_type": "code", "execution_count": 66, "id": "fa3da19e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10 entries, 0 to 9\n", "Data columns (total 4 columns):\n", " #   Column     Non-Null Count  Dtype  \n", "---  ------     --------------  -----  \n", " 0   Country    10 non-null     object \n", " 1   Age        9 non-null      float64\n", " 2   Salary     9 non-null      float64\n", " 3   Purchased  10 non-null     object \n", "dtypes: float64(2), object(2)\n", "memory usage: 452.0+ bytes\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 67, "id": "6049fb7d", "metadata": {}, "outputs": [], "source": ["#Step 3\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer( strategy='mean')\n", "data[[\"Age\", \"Salary\"]] = imputer.fit_transform(data[[\"Age\", \"Salary\"]])\n"]}, {"cell_type": "code", "execution_count": 68, "id": "3bf65ca4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>Age</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Purchased</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>France</td>\n", "      <td>44.000000</td>\n", "      <td>72000.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Spain</td>\n", "      <td>27.000000</td>\n", "      <td>48000.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Germany</td>\n", "      <td>30.000000</td>\n", "      <td>54000.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Spain</td>\n", "      <td>38.000000</td>\n", "      <td>61000.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Germany</td>\n", "      <td>40.000000</td>\n", "      <td>63777.777778</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>France</td>\n", "      <td>35.000000</td>\n", "      <td>58000.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Spain</td>\n", "      <td>38.777778</td>\n", "      <td>52000.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>France</td>\n", "      <td>48.000000</td>\n", "      <td>79000.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Germany</td>\n", "      <td>50.000000</td>\n", "      <td>83000.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>France</td>\n", "      <td>37.000000</td>\n", "      <td>67000.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Country        Age        Salary  Purchased\n", "0   France  44.000000  72000.000000          0\n", "1    Spain  27.000000  48000.000000          1\n", "2  Germany  30.000000  54000.000000          0\n", "3    Spain  38.000000  61000.000000          0\n", "4  Germany  40.000000  63777.777778          1\n", "5   France  35.000000  58000.000000          1\n", "6    Spain  38.777778  52000.000000          0\n", "7   France  48.000000  79000.000000          1\n", "8  Germany  50.000000  83000.000000          0\n", "9   France  37.000000  67000.000000          1"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["#Step 4\n", "from sklearn.preprocessing import LabelEncoder\n", "le = LabelEncoder()\n", "data[\"Purchased\"] = le.fit_transform(data[\"Purchased\"])\n", "data"]}, {"cell_type": "code", "execution_count": 79, "id": "a31c7c77", "metadata": {}, "outputs": [], "source": ["#Step 5\n", "from sklearn.model_selection import train_test_split\n", "\n", "x=data.drop(columns=[\"Purchased\"],axis=1)\n", "y=data['Purchased']\n", "x= pd.get_dummies(x, columns=[\"Country\"], drop_first=True)\n", "x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": 80, "id": "299422ec", "metadata": {}, "outputs": [], "source": ["#Step 6\n", "from sklearn.preprocessing import StandardScaler\n", "scaler = StandardScaler()\n", "\n", "x_train = scaler.fit_transform(x_train)\n", "x_test = scaler.transform(x_test)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}